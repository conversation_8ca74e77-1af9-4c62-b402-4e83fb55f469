import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  Image,
  ActivityIndicator,
} from 'react-native';
import React, { useEffect, useState } from 'react';
import { ScrollView } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Spinner from '../common/spinner';
import { Pressable } from 'react-native';
import { fonts, heightPixel, widthPixel } from '../../styles';
import { getSettings } from '../../../config';
import { useOrders, useCurrentUser, useUser } from '@appmaker-xyz/shopify';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { EventRegister } from 'react-native-event-listeners';
export default function Edd({ variants, selectedVariant }) {
  const [pin, setPin] = useState();
  const [pinText, setPinText] = useState('');
  const [loading, setLoading] = useState(false);
  const [finalEdd, setFinalEdd] = useState('');
  const settings = getSettings();

  // Get current user and orders
  const { id: currentUserId } = useCurrentUser({}) || {};
  const { orderList, isLoading: ordersLoading, refetch: refetchOrders } = useOrders({
    limit: 1
  });
console.log("order", orderList)
  // Enhanced order debugging

  const extractProductVariantId = variants.map((value) => {
    return { variantId: value.node.id.split('/ProductVariant/')[1] };
  });

  // Function to clear pincode from local storage (called on logout)
  const clearPincodeFromStorage = async () => {
    try {
      await AsyncStorage.removeItem('lastUsedPincode');
      console.log('✅ Pincode cleared from storage on logout');
    } catch (error) {
      console.error('❌ Error clearing pincode from storage:', error);
    }
  };

  // Function to save pincode to local storage with timestamp (ISO format like order)
  const savePincodeToStorage = async (pincode) => {
    try {
      // NEVER save default pincode (560103) to storage
      if (pincode === '560103') {
        console.log('🚫 Default pincode not saved to storage:', pincode);
        return;
      }

      const pincodeData = {
        pincode: pincode,
        timestamp: new Date().toISOString() // Save in ISO format like order processedAt
      };
      await AsyncStorage.setItem('lastUsedPincode', JSON.stringify(pincodeData));
      console.log('✅ Pincode saved to storage with ISO timestamp:', pincodeData);
    } catch (error) {
      console.error('❌ Error saving pincode to storage:', error);
    }
  };

  // Function to get pincode from local storage
  const getPincodeFromStorage = async () => {
    try {
      const storedData = await AsyncStorage.getItem('lastUsedPincode');
      if (storedData) {
        try {
          const parsedData = JSON.parse(storedData);

          // Check if timestamp is in old Unix format (number) and migrate to ISO format
          if (typeof parsedData.timestamp === 'number') {
            console.log('Migrating Unix timestamp to ISO format');
            const migratedData = {
              pincode: parsedData.pincode,
              timestamp: new Date(parsedData.timestamp).toISOString()
            };
            await AsyncStorage.setItem('lastUsedPincode', JSON.stringify(migratedData));
            console.log('Migrated pincode data:', migratedData);
            return migratedData;
          }

          // Return object with pincode and timestamp (already in ISO format)
          return parsedData;
        } catch (parseError) {
          // Handle very old format (just string) - migrate to new format
          console.log('Migrating very old pincode format (string only) to new format');
          const pincodeData = {
            pincode: storedData,
            timestamp: new Date().toISOString()
          };
          await AsyncStorage.setItem('lastUsedPincode', JSON.stringify(pincodeData));
          return pincodeData;
        }
      }
      return null;
    } catch (error) {
      console.error('Error getting pincode from storage:', error);
      return null;
    }
  };

  // Function to extract pincode with priority based on user requirements
  const prefillPincode = async (forceRefreshOrders = false) => {
    try {
      console.log('🚀 === PREFILL PINCODE DEBUG START ===');
      console.log('📊 Current state:', {
        currentUserId: !!currentUserId,
        ordersLoading,
        pin: pin,
        forceRefreshOrders
      });

      let pincodeToUse = null;
      let source = '';

      // Optionally refresh orders if requested
      if (forceRefreshOrders && refetchOrders) {
        console.log('🔄 Refreshing orders data...');
        try {
          await refetchOrders();
        } catch (error) {
          console.log('Error refreshing orders:', error);
        }
      }

      // Get stored pincode data (with timestamp)
      const storedPincodeData = await getPincodeFromStorage();
      console.log('💾 Stored pincode data:', storedPincodeData);

      // Get order pincode and its timestamp (if user is logged in)
      let orderPincode = null;
      let orderTimestamp = null;



      if (currentUserId && !ordersLoading && orderList && orderList.length > 0) {
        console.log('✅ ORDER CONDITIONS MET - Processing order data');
        const latestOrder = orderList[0]?.node;


        // Check for pincode in different possible field names
        console.log('🔍 SEARCHING FOR PINCODE IN ORDER:');
        const zipField = latestOrder?.shippingAddress?.zip;
        const postalCodeField = latestOrder?.shippingAddress?.postalCode;
        const zipCodeField = latestOrder?.shippingAddress?.zipCode;
        const pincodeField = latestOrder?.shippingAddress?.pincode;



        const possiblePincode = zipField || postalCodeField || zipCodeField || pincodeField;
        console.log('- Final possiblePincode:', possiblePincode);

        if (possiblePincode) {
          orderPincode = possiblePincode;
          // Keep order timestamp in ISO format (no conversion needed)
          orderTimestamp = latestOrder.processedAt || latestOrder.createdAt || null;
        
        } else {
          console.log('❌ NO PINCODE FOUND IN ORDER - checked all possible fields');
        }
      } else {
        console.log('❌ ORDER CONDITIONS NOT MET:');
        console.log('- currentUserId:', currentUserId);
        console.log('- ordersLoading:', ordersLoading);
        console.log('- orderList exists:', !!orderList);
        console.log('- orderList length:', orderList?.length);
        console.log('- Reason: User not logged in, orders still loading, or no orders found');
      }

      console.log('🎯 FINAL DECISION MAKING:');
      console.log('- storedPincodeData:', storedPincodeData);
      console.log('- orderPincode:', orderPincode);
      console.log('- orderTimestamp:', orderTimestamp);
      console.log('- Both exist?', !!(storedPincodeData && orderPincode));

      // Enhanced debugging for timestamp comparison (both in ISO format now)
      if (storedPincodeData && orderPincode && orderTimestamp) {
        console.log('🔍 TIMESTAMP COMPARISON DEBUG (ISO FORMAT):');
        console.log('Order timestamp (ISO):', orderTimestamp);
        console.log('Saved timestamp (ISO):', storedPincodeData.timestamp);

        // Convert both to Date objects for comparison
        const orderDate = new Date(orderTimestamp);
        const savedDate = new Date(storedPincodeData.timestamp);

        console.log('Order Date object:', orderDate);
        console.log('Saved Date object:', savedDate);
        console.log('Order > Saved?', orderDate > savedDate);
        console.log('Difference (ms):', orderDate.getTime() - savedDate.getTime());
        console.log('Difference (hours):', (orderDate.getTime() - savedDate.getTime()) / (1000 * 60 * 60));
      }

      // PRIORITY LOGIC based on user requirements:
      // 1. If user is logged in + has saved pincode + has orders → Use most recent (timestamp comparison)
      // 2. If user is logged in + has saved pincode + no orders → Use saved pincode
      // 3. If user is logged in + no saved pincode + has orders → Use last order pincode
      // 4. If user is logged in + no saved pincode + no orders → Use default pincode
      // 5. If user is logged out → Use saved pincode if exists, otherwise default pincode

      console.log('🎯 PRIORITY DECISION LOGIC:');
      console.log('- User logged in:', !!currentUserId);
      console.log('- Has saved pincode:', !!storedPincodeData);
      console.log('- Has order pincode:', !!orderPincode);

      if (currentUserId) {
        // User is logged in
        if (storedPincodeData && orderPincode) {
          // Both exist, compare timestamps to use the most recent one
          if (orderTimestamp) {
            const orderDate = new Date(orderTimestamp);
            const savedDate = new Date(storedPincodeData.timestamp);
            const timeDifferenceMinutes = (savedDate.getTime() - orderDate.getTime()) / (1000 * 60);

            console.log('🔍 TIMESTAMP COMPARISON:');
            console.log('Order timestamp (ISO):', orderTimestamp);
            console.log('Saved timestamp (ISO):', storedPincodeData.timestamp);
            console.log('Time difference (minutes):', timeDifferenceMinutes);

            if (timeDifferenceMinutes > 0) {
              // Saved pincode is more recent
              pincodeToUse = storedPincodeData.pincode;
              source = 'saved pincode (more recent)';
              console.log('✅ Using saved pincode (more recent):', pincodeToUse);
            } else {
              // Order pincode is more recent or same time
              pincodeToUse = orderPincode;
              source = 'order pincode (more recent)';
              console.log('✅ Using order pincode (more recent):', pincodeToUse);
            }
          } else {
            // No valid order timestamp, use saved pincode
            pincodeToUse = storedPincodeData.pincode;
            source = 'saved pincode (no valid order timestamp)';
            console.log('✅ Using saved pincode (no valid order timestamp):', pincodeToUse);
          }
        } else if (storedPincodeData) {
          // Logged in + saved pincode + no orders
          pincodeToUse = storedPincodeData.pincode;
          source = 'saved pincode (no orders)';
          console.log('✅ Using saved pincode (no orders):', pincodeToUse);
        } else if (orderPincode) {
          // Logged in + no saved pincode + has orders
          pincodeToUse = orderPincode;
          source = 'order pincode (no saved pincode)';
          console.log('✅ Using order pincode (no saved pincode):', pincodeToUse);
        } else {
          // Logged in + no saved pincode + no orders
          pincodeToUse = '560103';
          source = 'default (logged in, no data)';
          console.log('✅ Using default pincode (logged in, no data):', pincodeToUse);
        }
      } else {
        // User is logged out
        if (storedPincodeData) {
          // Logged out + has saved pincode
          pincodeToUse = storedPincodeData.pincode;
          source = 'saved pincode (logged out)';
          console.log('✅ Using saved pincode (logged out):', pincodeToUse);
        } else {
          // Logged out + no saved pincode
          pincodeToUse = '560103';
          source = 'default (logged out)';
          console.log('✅ Using default pincode (logged out):', pincodeToUse);
        }
      }

      console.log('🏁 FINAL RESULT:');
      console.log('- Selected pincode:', pincodeToUse);
      console.log('- Source:', source);
      console.log('- Current pin in input:', pin);
      console.log('- Will update input?', pincodeToUse !== pin);

      // Only proceed if pincode is different from current pin or pin is empty
      if (pincodeToUse !== pin) {
        console.log('✅ UPDATING PINCODE INPUT:', pincodeToUse);
        // Set the pincode in the input field
        setPin(pincodeToUse);

        // Save to local storage if it came from order (to maintain timestamp tracking)
        // The savePincodeToStorage function already handles not saving default pincode
        if (source.includes('order pincode')) {
          console.log('💾 Saving order pincode to storage for future reference');
          await savePincodeToStorage(pincodeToUse);
        }

        // Show loading state and automatically trigger EDD check
        setLoading(true);
        fetchRequestEDD(pincodeToUse);
      } else {
        console.log('⏭️ SKIPPING UPDATE - Pincode unchanged');
      }

      console.log('🚀 === PREFILL PINCODE DEBUG END ===');
    } catch (error) {
      console.error('❌ Error prefilling pincode:', error);
      setLoading(false);
    }
  };

  // State to track if we've already attempted to prefill
  const [hasPrefilled, setHasPrefilled] = useState(false);

  useEffect(() => {
    console.log('🔄 Component mounted - initializing');
    setLoading(false);
    // Reset prefill state when component mounts (new product)
    setHasPrefilled(false);

    // Listen for logout events to clear pincode data
    const logoutListener = EventRegister.addEventListener('user.logout', () => {
      console.log('🚪 User logout detected - clearing pincode data');
      clearPincodeFromStorage();
      // Reset pin to default for logged out users
      setPin('560103');
      setHasPrefilled(false);
      // Automatically check EDD with default pincode
      setLoading(true);
      fetchRequestEDD('560103');
    });

    // Cleanup listener on unmount
    return () => {
      if (logoutListener) {
        EventRegister.removeEventListener(logoutListener);
      }
    };
  }, []);

  // Effect to prefill pincode when user and orders are loaded (only once)
  useEffect(() => {
    if (currentUserId && !ordersLoading && !hasPrefilled) {
      prefillPincode();
      setHasPrefilled(true);
    }
  }, [currentUserId, ordersLoading, hasPrefilled]);



  // Effect to prefill pincode for guest users (from local storage only) or when user logs out
  useEffect(() => {
    console.log('👤 Guest user effect triggered:', {
      currentUserId: !!currentUserId,
      hasPrefilled,
      shouldTrigger: !currentUserId && !hasPrefilled
    });

    // For guest users (not logged in) or when user logs out, prefill from storage or default
    if (!currentUserId && !hasPrefilled) {
      console.log('🚀 Triggering prefill for guest user or after logout');
      prefillPincode();
      setHasPrefilled(true);
    }
  }, [currentUserId, hasPrefilled]);


  function handleDeliveryDetailbutton() {
    setLoading(true);

    if (!pin || pin.length !== 6) {
      setPinText('Please Enter a valid Pin');
      setLoading(false);
    } else {
      fetchRequestEDD(pin);
      setPinText('');
    }
  }

  const fetchRequestEDD = async (pinData) => {
    console.log("pinData", pinData, settings.edd_api_url);
    const response = await fetch(
      settings.edd_api_url,
      {
        method: 'POST',
        headers: {
          accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          dropPincode: pinData,
          quantity: 1,
          variantsData: extractProductVariantId,
        }),
      },
    );

    const responseData = await response.json();
    console.log("responseDataPin",responseData)
    if (responseData.serviceable) {
      if (responseData?.variantsData?.length)
        setFinalEdd(responseData?.variantsData?.[0]?.edd);
      setPinText('');
      setLoading(false);

      // Save pincode to storage only when it's valid, serviceable, and NOT the default pincode
      // The savePincodeToStorage function already handles the default pincode check
      if (pinData && pinData.length === 6) {
        savePincodeToStorage(pinData);
        console.log('✅ Attempting to save pincode after successful validation:', pinData);
      }
    } else {
      setLoading(false);
      setFinalEdd('');
      setPinText('Pincode not serviceable.');
    }
  };

  return (
    <View>
      <View
        style={{
          borderWidth: 0,
          elevation: 0,
          paddingVertical: heightPixel(8),
          borderRadius: widthPixel(10),
        }}>
        <Text
          style={{
            color: '#221f20',
            fontSize: fonts._16,
            fontFamily: fonts.FONT_FAMILY.SemiBold,
            marginBottom: heightPixel(10),
          }}>
          Delivery Details
        </Text>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            overflow: 'hidden',
            gap: widthPixel(8),
            flexWrap: 'wrap',
          }}>
          <View style={{ width: '60%' }}>
            <TextInput
              placeholder="Enter Your pincode"
              maxLength={6}
              keyboardType="numeric"
              onChangeText={(newPin) => {
                if (newPin === '') {
                  setPinText('');
                  setFinalEdd(''); // Clear EDD when input is cleared
                }
                const trimmedPin = newPin.trim();
                setPin(trimmedPin);

                // When user manually changes pincode, mark as user-modified to prevent auto-refill
                if (trimmedPin !== pin) {
                  console.log('📝 User manually changed pincode:', trimmedPin);
                }
              }}
              value={pin}
              style={{
                borderWidth: 1,
                borderColor: 'rgba(34,31,32,.1)',
                padding: widthPixel(8),
                borderRadius: widthPixel(8),
                fontFamily: 'Jost-SemiBold',
                paddingLeft: widthPixel(20),
                height: heightPixel(50),
                alignItems: 'center', justifyContent: 'center',
              }}
            />
          </View>
          <View style={{ width: '32%' }}>
            <Pressable
              onPress={() => {
                handleDeliveryDetailbutton();
              }}>
              <LinearGradient
                colors={['#221f20', '#505050']}
                start={{ x: -0.3336, y: 0 }} // Adjusted based on the angle in the CSS
                end={{ x: 1.3952, y: 1 }} // Adjusted based on the angle in the CSS
                style={{
                  paddingHorizontal: widthPixel(10),
                  // paddingVertical: heightPixel(13),
                  height: heightPixel(50),
                  alignItems: 'center', justifyContent: 'center',
                  borderRadius: widthPixel(10),
                }}>
                {!loading ? (
                  <Text style={styles.buttonText}>CHECK</Text>
                ) : (
                  <View
                    style={{
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                    {/* <Spinner /> */}
                    <ActivityIndicator size={'small'} color={'white'} />
                  </View>
                )}
              </LinearGradient>
            </Pressable>
          </View>
        </View>

        {pinText != '' && (
          <View style={{ paddingVertical: heightPixel(10) }}>
            <Text
              style={{ color: 'red', fontFamily: fonts.FONT_FAMILY.Regular }}>
              {pinText}
            </Text>
          </View>
        )}

        {/* EDD details , div needs to be visible when pincode entered  */}
        {finalEdd ? (
          <View>
            <ScrollView
              horizontal={true}
              style={{
                paddingVertical: heightPixel(10),
                borderWidth: 0,
                marginVertical: heightPixel(10),
                width: '100%',
              }}
              contentContainerStyle={{
                paddingRight: widthPixel(50),
              }}>
              <View style={{ display: 'flex', flexDirection: 'row' }}>
                <View
                  style={{
                    padding: widthPixel(10),
                    borderRadius: widthPixel(7),
                    borderWidth: 1,
                    borderColor: 'rgba(34,31,32,.1)',
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    width: '60%',
                    marginRight: widthPixel(10),
                    height: heightPixel(50),
                  }}>
                  <View
                    style={{ width: widthPixel(20), height: widthPixel(20) }}>
                    <Image
                      source={{
                        uri: 'https://www.jockey.in/cdn/shop/files/Group_239103.jpg?v=16732760107086212298',
                      }}
                      style={{
                        width: '100%',
                        height: '100%',
                        resizeMode: 'contain',
                      }}
                    />
                  </View>
                  <View>
                    <Text
                      style={{
                        fontSize: fonts._14,
                        fontFamily: fonts.FONT_FAMILY.Regular,
                      }}>
                      Estimated Delivery by{' '}
                      <Text
                        style={{
                          fontSize: fonts._13,
                          fontFamily: fonts.FONT_FAMILY.Bold,
                        }}>
                        {finalEdd}
                      </Text>
                    </Text>
                  </View>
                </View>

                {/* 3 */}
                <View
                  style={{
                    padding: widthPixel(10),
                    borderRadius: widthPixel(7),
                    borderWidth: 1,
                    borderColor: 'rgba(34,31,32,.1)',
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    width: '40%',
                  }}>
                  <View
                    style={{ width: widthPixel(20), height: widthPixel(20) }}>
                    <Image
                      source={{
                        uri: 'https://www.jockey.in/cdn/shop/files/Vector_1_a29858f8-e63b-4de4-9be6-3f78849099e3.png?v=9477741078544455416',
                      }}
                      style={{
                        width: '100%',
                        height: '100%',
                        resizeMode: 'contain',
                      }}
                    />
                  </View>
                  <Text
                    style={{
                      fontSize: fonts._13,
                      fontFamily: fonts.FONT_FAMILY.Regular,
                    }}>
                    {'Eligible for Free Delivery'}
                  </Text>
                </View>
              </View>
            </ScrollView>
          </View>
        ) : (
          <View></View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  buttonText: {
    fontSize: fonts._14,
    color: 'white',
    textAlign: 'center',
    fontFamily: fonts.FONT_FAMILY.Regular,
  },
});
